"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\"),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-multiple\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            const numericValue = parseInt(value.replace(/\\D/g, \"\"));\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().when(\"modalidade\", {\n        is: (val)=>val === \"SCP\",\n        then: (schema)=>schema.required(\"Quantidade de cotas obrigat\\xf3ria\"),\n        otherwise: (schema)=>schema.notRequired()\n    }),\n    // Campos de IR (mutuamente exclusivos)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                setValue(\"cpf\", contractData.document || \"\");\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                setValue(\"celular\", contractData.phone || \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                setValue(\"cep\", contractData.zipCode || \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                setValue(\"valorInvestimento\", contract.investmentValue || \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue,\n        watch\n    ]);\n    // Calcular valor do investimento automaticamente para SCP baseado na quantidade de cotas\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const modalidade = watch(\"modalidade\");\n        const quotaQuantity = watch(\"quotaQuantity\");\n        if (modalidade === \"SCP\" && quotaQuantity) {\n            const cotas = parseInt(quotaQuantity) || 0;\n            const valorTotal = cotas * 5000;\n            setValue(\"valorInvestimento\", valorTotal.toLocaleString(\"pt-BR\", {\n                style: \"currency\",\n                currency: \"BRL\"\n            }));\n        }\n    }, [\n        watch(\"modalidade\"),\n        watch(\"quotaQuantity\"),\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    function calcularAliquotaIR() {\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n    }\n    const onSubmit = async (data)=>{\n        var _contractData_contracts_, _contractData_contracts;\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Usar o contractId do primeiro contrato retornado pela API\n        const contractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.id;\n        console.log(\"Contract ID:\", contractId);\n        console.log(\"Investor ID:\", investorId);\n        if (!contractId) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"ID do contrato n\\xe3o encontrado nos dados carregados\");\n            return;\n        }\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto JSON em vez de FormData\n            const requestData = {\n                role: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"\",\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                bankAccount: {\n                    bank: data.banco,\n                    agency: data.agencia,\n                    account: data.conta,\n                    pix: data.chavePix,\n                    type: \"CORRENTE\"\n                },\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")),\n                    monthlyRate: parseFloat(data.taxaRemuneracao),\n                    durationInMonths: parseInt(data.prazoInvestimento),\n                    paymentMethod: data.comprarCom,\n                    startDate: data.inicioContrato,\n                    endDate: data.fimContrato,\n                    profile: data.perfil,\n                    isDebenture: data.debenture === \"s\",\n                    ...data.modalidade === \"SCP\" && {\n                        quotaQuantity: parseInt(data.quotaQuantity || \"0\")\n                    }\n                }\n            };\n            if (isPJ) {\n                requestData.company = {\n                    corporateName: data.nomeCompleto,\n                    cnpj: documento\n                };\n            } else {\n                requestData.individual = {\n                    fullName: data.nomeCompleto,\n                    cpf: documento,\n                    rg: data.identidade,\n                    birthDate: data.dataNascimento,\n                    email: data.email,\n                    phone: data.celular.replace(/\\D/g, \"\"),\n                    motherName: data.nomeMae,\n                    nationality: \"brasileira\",\n                    occupation: \"Investidor\",\n                    issuingAgency: \"SSP\",\n                    address: {\n                        street: data.endereco,\n                        city: data.cidade,\n                        state: data.estado || \"\",\n                        postalCode: data.cep.replace(/\\D/g, \"\"),\n                        number: data.numero,\n                        neighborhood: \"Centro\",\n                        complement: data.complemento || \"\"\n                    }\n                };\n            }\n            console.log(\"Enviando dados para API...\", requestData);\n            console.log(\"Usando Contract ID para upgrade:\", contractId);\n            upgradeContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const upgradeContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useMutation)({\n        mutationFn: async (data)=>{\n            var _contractData_contracts_, _contractData_contracts;\n            // Usar o contractId do primeiro contrato retornado pela API\n            const contractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.id;\n            console.log(\"Mutation - Contract Data:\", contractData);\n            console.log(\"Mutation - Contract ID:\", contractId);\n            if (!contractId) {\n                throw new Error(\"ID do contrato n\\xe3o encontrado nos dados carregados\");\n            }\n            console.log(\"Fazendo PUT para:\", \"/contract/\".concat(contractId, \"/upgrade\"));\n            return _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].put(\"/contract/\" + contractId + \"/upgrade\", data);\n        },\n        onSuccess: (response)=>{\n            var _response_data;\n            console.log(\"Upgrade realizado com sucesso:\", response.data);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Upgrade realizado com sucesso! Novo contrato: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Upgrade realizado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Remove tudo que não for número ou vírgula\n        const limpo = valor.replace(/[^0-9,]/g, \"\").replace(\",\", \".\");\n        return parseFloat(limpo) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 15,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 764,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 811,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 812,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 823,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 593,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_quotaQuantity, _errors_valorInvestimento, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>setValue(\"modalidade\", e.target.value, {\n                                                    shouldValidate: true\n                                                }),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"quotaQuantity\",\n                                        width: \"100%\",\n                                        error: !!errors.quotaQuantity,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                        label: \"Quantidade de cotas\",\n                                        placeholder: \"ex: 10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: calcularAliquotaIR,\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 876,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 910,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 913,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 933,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 884,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDeposito\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDeposito\", true);\n                                                    setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDeposito\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDesconto\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDesconto\", true);\n                                                    setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDesconto\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 979,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 962,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message,\n                                        label: \"Valor do Investimento\",\n                                        disabled: watch(\"modalidade\") === \"SCP\",\n                                        setValue: (e)=>watch(\"modalidade\") === \"SCP\" ? undefined : setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1059,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1069,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 999,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1092,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || upgradeContractMutation.isPending || isRedirecting,\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || upgradeContractMutation.isPending ? \"Enviando...\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1091,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 834,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1134,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1133,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1132,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1150,\n                                columnNumber: 13\n                            }, this),\n                            step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1149,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"BC4r5LHmvPKZ+tMg8YfQ3V0EoV8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_17__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});